.
├── docs
│   └── file-system.md
├── .env
├── eslint.config.js
├── .gitignore
├── index.html
├── package.json
├── package-lock.json
├── preamble_enygma.txt
├── public
│   └── azure-voice-conf.json
├── README.md
├── src
│   ├── App.css
│   ├── App.tsx
│   ├── components
│   │   └── SimpleVoiceChat.tsx
│   ├── hooks
│   │   └── useRealTimeConversation.ts
│   ├── index.css
│   ├── main.tsx
│   ├── services
│   │   ├── AppService.ts
│   │   ├── ConversationStorage.ts
│   │   ├── impl
│   │   │   ├── IAppService.ts
│   │   │   ├── ISpeechRecognitionService.ts
│   │   │   └── IVoicesService.ts
│   │   ├── SpeechRecognitionService.ts
│   │   └── VoicesService.ts
│   ├── types
│   │   ├── GenerateInput.ts
│   │   ├── GenerateResponse.ts
│   │   └── SelectedPresetResponse.ts
│   ├── utils
│   │   ├── audioUtils.ts
│   │   └── gameUtils.ts
│   └── vite-env.d.ts
├── tsconfig.app.json
├── tsconfig.json
├── tsconfig.node.json
└── vite.config.ts

10 directories, 33 files
