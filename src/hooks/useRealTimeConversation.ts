import { useState, useEffect, useCallback, useRef } from "react";
import { SpeechRecognitionService } from "../services/SpeechRecognitionService";
import { AppService } from "../services/AppService";
import {
  ConversationStorage,
  type StoredMessage,
} from "../services/ConversationStorage";
import type {
  ConversationState,
  SpeechRecognitionResult,
} from "../services/impl/ISpeechRecognitionService";

export interface ConversationMessage {
  id: string;
  type: "user" | "ai";
  content: string;
  timestamp: Date;
  isInterim?: boolean;
}

export interface UseRealTimeConversationReturn {
  // Estado
  isActive: boolean;
  conversationState: ConversationState;
  messages: ConversationMessage[];
  currentUserInput: string;
  isSupported: boolean;
  error: string | null;

  // Controles
  startConversation: () => Promise<boolean>;
  stopConversation: () => void;
  sendMessage: (message: string) => Promise<void>;
  clearMessages: () => void;
  addInitialMessage: (content: string) => void; // 👈 Nuevo método

  // Configuración
  enableSmartMicrophone: () => void;
  disableSmartMicrophone: () => void;
}

// Convertir StoredMessage a ConversationMessage
const convertStoredToConversation = (
  stored: StoredMessage
): ConversationMessage => ({
  id: stored.id,
  type: stored.role === "user" ? "user" : "ai",
  content: stored.content,
  timestamp: new Date(stored.createdAt),
  isInterim: false,
});

export const useRealTimeConversation = (
  generatedCharacter?: string,
  isGameStarted?: boolean
): UseRealTimeConversationReturn => {
  const [isActive, setIsActive] = useState(false);
  const [conversationState, setConversationState] =
    useState<ConversationState>("idle");
  const [messages, setMessages] = useState<ConversationMessage[]>([]);
  const [currentUserInput, setCurrentUserInput] = useState("");
  const [error, setError] = useState<string | null>(null);

  const speechService = useRef(SpeechRecognitionService.getInstance());
  const appService = useRef(AppService.getInstance());
  const storage = useRef(ConversationStorage.getInstance());
  const interimMessageId = useRef<string | null>(null);

  const isSupported = speechService.current.isSupported();

  // Cargar mensajes desde localStorage al inicializar
  useEffect(() => {
    const loadStoredMessages = () => {
      const session = storage.current.loadLatestSession();
      if (session && session.messages.length > 0) {
        const convertedMessages = session.messages.map(
          convertStoredToConversation
        );
        setMessages(convertedMessages);
        // console.log(
        //   "📖 Mensajes cargados desde localStorage:",
        //   convertedMessages.length
        // );
      }
    };

    loadStoredMessages();
  }, []);

  // Crear nueva sesión cuando cambia el personaje generado
  useEffect(() => {
    if (generatedCharacter) {
      storage.current.createNewSession(generatedCharacter);
      // console.log("🆕 Nueva sesión creada para personaje:", generatedCharacter);
    }
  }, [generatedCharacter]);

  // Agregar mensaje inicial de la IA (seed message)
  const addInitialMessage = useCallback((content: string) => {
    try {
      const seedMessage = storage.current.addSeedMessage(content);
      const convertedMessage = convertStoredToConversation(seedMessage);

      // Verificar si ya existe este mensaje en el estado
      setMessages((prev) => {
        const existingSeed = prev.find((m) => m.id === "seed-1");
        if (existingSeed) {
          return prev; // Ya existe, no agregar duplicado
        }
        return [convertedMessage, ...prev]; // Agregar al inicio
      });

      // console.log("🌱 Mensaje inicial agregado al storage y estado");
    } catch (error) {
      console.error("❌ Error agregando mensaje inicial:", error);
    }
  }, []);

  // Agregar mensaje regular al historial
  const addMessage = useCallback(
    (type: "user" | "ai", content: string, isInterim = false) => {
      if (!content.trim()) return "";

      const timestamp = new Date();
      const messageId = `${type}_${timestamp.getTime()}`;

      const newMessage: ConversationMessage = {
        id: messageId,
        type,
        content,
        timestamp,
        isInterim,
      };

      if (isInterim) {
        // Mensaje temporal - solo en estado, no en storage
        interimMessageId.current = messageId;
        setMessages((prev) => {
          const filtered = prev.filter((m) => !m.isInterim || m.type !== type);
          return [...filtered, newMessage];
        });
      } else {
        // Mensaje final - agregar a storage y estado
        try {
          const role = type === "user" ? "user" : "assistant";
          const storedMessage = storage.current.addMessage(role, content);
          const convertedMessage = convertStoredToConversation(storedMessage);

          setMessages((prev) => {
            // Remover mensajes interim del mismo tipo
            const filtered = prev.filter(
              (m) => !m.isInterim || m.type !== type
            );
            return [...filtered, convertedMessage];
          });

          interimMessageId.current = null;
        } catch (error) {
          console.error("❌ Error guardando mensaje:", error);
          // Fallback: agregar solo al estado
          setMessages((prev) => {
            const filtered = prev.filter(
              (m) => !m.isInterim || m.type !== type
            );
            return [...filtered, newMessage];
          });
        }
      }

      return messageId;
    },
    []
  );

  // Finalizar mensaje interim
  const finalizeMessage = useCallback(
    (content: string) => {
      if (interimMessageId.current) {
        // Reemplazar el mensaje interim con uno final
        addMessage("user", content, false);
      }
    },
    [addMessage]
  );

  // Enviar mensaje a la IA
  const sendMessage = useCallback(
    async (message: string) => {
      if (!message.trim()) return;

      try {
        setConversationState("processing");
        speechService.current.setConversationState("processing");

        // Don't include character info in the message - let the server template handle it
        const response = await appService.current.generateWithIaVsPlayer(
          message,
          undefined, // id
          generatedCharacter // personaje
        );
        const responseText =
          response.response ||
          response.output ||
          response.result ||
          response.text ||
          response.content ||
          "Respuesta no encontrada";

        // Agregar respuesta de la IA al historial
        addMessage("ai", responseText);

        // Cambiar estado a "hablando"
        setConversationState("speaking");
        speechService.current.setConversationState("speaking");
      } catch (error) {
        console.error("❌ Error procesando mensaje:", error);
        setError("Error al procesar el mensaje");
        setConversationState("idle");
        speechService.current.setConversationState("idle");
      }
    },
    [generatedCharacter, addMessage]
  );

  // Manejar resultados del reconocimiento de voz
  const handleSpeechResult = useCallback(
    (result: SpeechRecognitionResult) => {
      const { transcript, isFinal } = result;

      if (transcript.trim()) {
        // Ignorar transcripciones que coincidan con el mensaje inicial de la IA
        if (transcript.includes("ya estoy pensando en un personaje")) {
          console.log(
            "🚫 Ignorando transcripción que parece ser eco del audio de la IA"
          );
          return;
        }

        if (isFinal) {
          finalizeMessage(transcript);
          setCurrentUserInput("");
          sendMessage(transcript);
        } else {
          addMessage("user", transcript, true);
          setCurrentUserInput(transcript);
        }
      }
    },
    [finalizeMessage, sendMessage, addMessage]
  );

  // Manejar cambios de estado
  const handleStateChange = useCallback((change: any) => {
    setConversationState(change.state);
  }, []);

  // Manejar errores
  const handleError = useCallback((errorMessage: string) => {
    setError(errorMessage);
    setConversationState("idle");
  }, []);

  // Iniciar conversación
  const startConversation = useCallback(async (): Promise<boolean> => {
    if (!isSupported) {
      const errorMsg =
        "El reconocimiento de voz no está soportado en este navegador";
      setError(errorMsg);
      return false;
    }

    setError(null);
    setIsActive(true);

    const success = await speechService.current.startListening();
    if (!success) {
      setIsActive(false);
      const errorMsg = "No se pudo iniciar el reconocimiento de voz";
      setError(errorMsg);
      return false;
    }

    // return true;
    return success;
  }, [isSupported]);

  // Manejar cuando el audio termine
const handleAudioFinished = useCallback(() => {
  console.log("🎤 Audio finished callback ejecutado");

  if ((window as any).audioCallbackExecuted) {
    console.log("🎤 Callback ya ejecutado anteriormente, saltando");
    return;
  }

  // ✅ CORREGIDO: Siempre sincronizar estados cuando termine el audio
  console.log("🎤 Sincronizando estados tras finalización de audio...");
  setConversationState("idle");
  speechService.current.setConversationState("idle");

  // ✅ NUEVO: Auto-activar conversación cuando termine el PRIMER audio
  if (!isActive && isGameStarted && isSupported) {
    console.log("🎤 Primer audio terminado, auto-activando conversación...");

    // ✅ CORREGIDO: Función recursiva que espera hasta que los estados se sincronicen
    const waitForIdleAndActivate = async (attempts = 0) => {
      const maxAttempts = 10; // Máximo 5 segundos (10 * 500ms)

      console.log(`🔍 Intento ${attempts + 1}: Verificando estados para auto-activación...`);
      console.log("🔍 Estados actuales:", {
        speechServiceState: speechService.current.getConversationState(),
        useEffectState: conversationState,
        isAudioPlaying: (window as any).currentAudio && !(window as any).currentAudio.ended,
        speechServiceListening: speechService.current.isListening()
      });

      // ✅ Verificar que AMBOS estados estén en idle/correcto
      const speechServiceIdle = speechService.current.getConversationState() === "idle";
      const useEffectIdle = conversationState === "idle";
      const noAudioPlaying = !(window as any).currentAudio || (window as any).currentAudio.ended;
      const notListening = !speechService.current.isListening();

      if (speechServiceIdle && useEffectIdle && noAudioPlaying && notListening) {
        console.log("✅ Estados sincronizados, activando conversación...");

        try {
          const success = await startConversation();
          console.log("🎤 Resultado de startConversation:", success);

          if (success) {
            console.log("✅ Conversación auto-activada tras primer audio");
            speechService.current.enableSmartMicrophoneControl();

            // ✅ NUEVO: Activar micrófono inmediatamente después de activar conversación
            setTimeout(() => {
              console.log("🎤 Activando micrófono tras auto-activación...");
              speechService.current.startListening();
            }, 1000);
          } else {
            console.warn("⚠️ startConversation falló tras sincronización");
          }
        } catch (error) {
          console.error("❌ Error auto-activando tras sincronización:", error);
        }

        return; // Salir exitosamente
      }

      // ✅ Si no están sincronizados y no hemos excedido intentos, volver a intentar
      if (attempts < maxAttempts) {
        console.log(`⏳ Estados no sincronizados, reintentando en 500ms... (${attempts + 1}/${maxAttempts})`);
        setTimeout(() => waitForIdleAndActivate(attempts + 1), 500);
      } else {
        console.warn("⚠️ Timeout esperando sincronización de estados, abandonando auto-activación");
      }
    };

    // ✅ Marcar callback como ejecutado ANTES de empezar
    (window as any).audioCallbackExecuted = true;

    // Empezar el proceso de espera y activación
    setTimeout(() => waitForIdleAndActivate(), 500);

    return; // Salir temprano para el primer audio
  }

  // ✅ RESTO: Lógica normal para audios posteriores (sin cambios)
  if (!isActive) {
    console.log("❌ Conversación no está activa, no se puede reactivar micrófono");
    return;
  }

  if (isActive) {
    console.log("🎤 Cambiando estado a idle...");
    setConversationState("idle");
    speechService.current.setConversationState("idle");

    setTimeout(() => {
      const shouldReactivate =
        speechService.current.shouldBeListening() &&
        !speechService.current.isListening();

      console.log("🎤 Evaluando reactivación:", {
        shouldBeListening: speechService.current.shouldBeListening(),
        isListening: speechService.current.isListening(),
        shouldReactivate,
        conversationState: speechService.current.getConversationState(),
        isActive,
      });

      if (shouldReactivate) {
        console.log("🎤 Reactivando micrófono tras audio...");
        speechService.current.startListening();
      }
    }, 2000);
  }
}, [isActive, conversationState, isGameStarted, isSupported, startConversation]);

  const handleAudioStarted = useCallback(() => {
    console.log("🔊 Audio started callback ejecutado");

    // Detener micrófono si está activo
    if (speechService.current.isListening()) {
      console.log("🔇 Deteniendo micrófono: audio iniciado");
      speechService.current.stopListening();
    }

    // Cambiar estados
    setConversationState("speaking");
    speechService.current.setConversationState("speaking");
  }, []);

  // Inicializar callbacks del servicio de voz
  useEffect(() => {
    speechService.current.onResult(handleSpeechResult);
    speechService.current.onStateChange(handleStateChange);
    speechService.current.onError(handleError);

    // Configurar callbacks de audio
    appService.current.setAudioFinishedCallback(handleAudioFinished);
    appService.current.setAudioStartedCallback(handleAudioStarted);
  }, [
    handleSpeechResult,
    handleStateChange,
    handleError,
    handleAudioFinished,
    handleAudioStarted,
  ]);

  // Detener conversación
  const stopConversation = useCallback(() => {
    speechService.current.stopListening();
    setIsActive(false);
    setConversationState("idle");
    setCurrentUserInput("");
  }, []);

  // Limpiar mensajes
  const clearMessages = useCallback(() => {
    setMessages([]);
    setCurrentUserInput("");
    setError(null);
    storage.current.clearAllConversations();
    console.log("🗑️ Historial de mensajes y todas las conversaciones limpiadas");
  }, []);

  // Control inteligente del micrófono
  const enableSmartMicrophone = useCallback(() => {
    speechService.current.enableSmartMicrophoneControl();
  }, []);

  const disableSmartMicrophone = useCallback(() => {
    speechService.current.disableSmartMicrophoneControl();
  }, []);

  // Cleanup al desmontar
  useEffect(() => {
    return () => {
      if (isActive) {
        stopConversation();
      }
    };
  }, [isActive, stopConversation]);

  return {
    // Estado
    isActive,
    conversationState,
    messages,
    currentUserInput,
    isSupported,
    error,

    // Controles
    startConversation,
    stopConversation,
    sendMessage,
    clearMessages,
    addInitialMessage, // 👈 Nuevo método expuesto

    // Configuración
    enableSmartMicrophone,
    disableSmartMicrophone,
  };
};
